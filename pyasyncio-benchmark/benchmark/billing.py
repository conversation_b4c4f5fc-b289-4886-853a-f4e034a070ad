import json

import boto3
import requests


def _get_instance_type() -> str:
    """Fetch running instance type from Instance Metadata Service.

    Must be called on a running EC2
    """
    token_url = "http://***************/latest/api/token"
    meta_data_instance_id = "http://***************/latest/meta-data/instance-type"

    headers = {"X-aws-ec2-metadata-token-ttl-seconds": "21600"}
    response = requests.put(token_url, headers=headers, timeout=1)

    headers = {"X-aws-ec2-metadata-token": response.text}
    response = requests.get(meta_data_instance_id, headers=headers)
    metadata = response.text

    return metadata


def _get_instance_price(instance_type: str) -> float:
    """Get hourly price (USD) of instance type in `us-east-1`."""
    client = boto3.client("pricing", region_name="us-east-1")
    data = client.get_products(
        ServiceCode="AmazonEC2",
        Filters=[
            {"Field": "instanceType", "Value": instance_type, "Type": "TERM_MATCH"},
            {"Field": "operatingSystem", "Value": "Linux", "Type": "TERM_MATCH"},
            {"Field": "capacitystatus", "Value": "Used", "Type": "TERM_MATCH"},
            {"Field": "preInstalledSw", "Value": "NA", "Type": "TERM_MATCH"},
            {"Field": "tenancy", "Value": "shared", "Type": "TERM_MATCH"},
            {
                "Field": "location",
                "Value": "US East (N. Virginia)",
                "Type": "TERM_MATCH",
            },
        ],
    )
    od = json.loads(data["PriceList"][0])["terms"]["OnDemand"]
    id1 = list(od)[0]
    id2 = list(od[id1]["priceDimensions"])[0]
    price = od[id1]["priceDimensions"][id2]["pricePerUnit"]["USD"]
    return float(price)


def is_ec2() -> bool:
    try:
        requests.get("http://***************/latest/meta-data/public-ipv4", timeout=1)
        return True
    except (
        requests.exceptions.ConnectTimeout,
        requests.exceptions.ConnectionError,
    ):
        return False


def get_ec2_billing_info() -> dict:
    """Get hourly price (USD) for running instance."""
    instance_type = _get_instance_type()
    return {
        "instance_type": instance_type,
        "hourly_cost": _get_instance_price(instance_type),
    }
