# Configuration to retest httpx fetch_range with the EXACT same settings as previous benchmark
# This replicates the parameterized conditions from the 20250430 results

tests:
  # Fetch Range test - replicating the exact parameterized conditions
  - library_name: httpx
    test_name: fetch_range
    timeout: -1  # No timeout (same as previous runs)
    n_requests: 50000  # High number to let it run until completion
    replicas: 1
    debug: false
    client_config:
      pool_size_per_host: 100  # Same as previous
      keep_alive: true
      keep_alive_timeout_seconds: 30
      use_dns_cache: true
    params:
      request_size:
        expression: "[16384, 32768, 65536, 131072, 262144, 524288, 1048576, 2097152, 4194304, 8388608]"

  # Also run aiohttp for comparison
  - library_name: aiohttp
    test_name: fetch_range
    timeout: -1
    n_requests: 50000
    replicas: 1
    debug: false
    client_config:
      pool_size_per_host: 100
      keep_alive: true
      keep_alive_timeout_seconds: 30
      use_dns_cache: true
    params:
      request_size:
        expression: "[16384, 32768, 65536, 131072, 262144, 524288, 1048576, 2097152, 4194304, 8388608]"
