# Configuration to retest httpx with the EXACT same settings as the previous benchmark
# This replicates the conditions from the 20250430 results

tests:
  # COG Header test - replicating the exact conditions from previous runs
  - library_name: httpx
    test_name: cog_header
    timeout: -1  # No timeout (same as previous runs)
    n_requests: 100000  # High number to let it run until completion
    replicas: 1
    debug: false
    client_config:
      pool_size_per_host: 100  # Same as previous: pool_size=100
      keep_alive: true         # Same as previous: keep_alive=True  
      keep_alive_timeout_seconds: 30  # Same as previous: keep_alive_timeout_seconds=30
      use_dns_cache: true      # Same as previous: use_dns_cache=True
    params: {}  # Empty params same as previous

  # Also run aiohttp for comparison with same settings
  - library_name: aiohttp
    test_name: cog_header
    timeout: -1
    n_requests: 100000
    replicas: 1
    debug: false
    client_config:
      pool_size_per_host: 100
      keep_alive: true
      keep_alive_timeout_seconds: 30
      use_dns_cache: true
    params: {}

  # Also run asynctiff for comparison
  - library_name: asynctiff
    test_name: cog_header
    timeout: -1
    n_requests: 100000
    replicas: 1
    debug: false
    client_config:
      pool_size_per_host: 100
      keep_alive: true
      keep_alive_timeout_seconds: 30
      use_dns_cache: true
    params: {}
