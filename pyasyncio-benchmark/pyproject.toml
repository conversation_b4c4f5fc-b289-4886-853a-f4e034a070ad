[tool.poetry]
name = "pyasyncio-benchmark"
version = "0.1.0"
description = "Benchmark python coroutines in various ways"
authors = ["geospatial-jeff <<EMAIL>>"]
license = "apache"
readme = "README.md"
packages = [{ include = "benchmark" }]

[tool.poetry.dependencies]
python = "^3.11"
obstore = "==0.4.*"
boto3 = "*"
aiohttp = "^3.10.10"
aioboto3 = "^13.2.0"
alembic = "^1.13.3"
requests_unixsocket2 = "^0.4.2"
pydantic-settings = "^2.7.0"
click = "^8.1.8"
docker = "^7.1.0"
pandas = "^2.2.3"
requests = "^2.32.3"
s3fs = "==2024.12.0"
"httpx[http2]" = "^0.28.1"
rasterio = "^1.4.3"
numpy = "^2.0.0"
async-tiff = "^0.1.0"
cog-layers = "^0.1.0"
yaml = "^6.0.2"

[tool.poetry.group.dev.dependencies]
pre-commit = "^4.0.1"

[tool.poetry.scripts]
benchmark = "benchmark.cli:app"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
