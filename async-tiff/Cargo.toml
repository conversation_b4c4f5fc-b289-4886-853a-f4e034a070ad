[package]
name = "async-tiff"
version = "0.1.0"
edition = "2021"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/developmentseed/async-tiff"
description = "Low-level asynchronous TIFF reader."
readme = "README.md"

[dependencies]
byteorder = "1"
bytes = "1.7.0"
flate2 = "1.0.20"
futures = "0.3.31"
jpeg = { package = "jpeg-decoder", version = "0.3.0", default-features = false }
num_enum = "0.7.3"
object_store = { version = "0.12", optional = true }
reqwest = { version = "0.12", default-features = false, optional = true }
thiserror = "1"
tokio = { version = "1.43.0", optional = true, default-features = false, features = [
    "io-util",
    "sync",
] }
weezl = "0.1.0"

[dev-dependencies]
object_store = { version = "0.12", features = ["http"] }
tiff = "0.9.1"
tokio = { version = "1.9", features = [
    "macros",
    "fs",
    "rt-multi-thread",
    "io-util",
] }
tokio-test = "0.4.4"

[features]
default = ["object_store", "reqwest"]
tokio = ["dep:tokio"]
reqwest = ["dep:reqwest"]
object_store = ["dep:object_store"]

[package.metadata.cargo-all-features]
