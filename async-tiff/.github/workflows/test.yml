name: Rust

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  lint-test:
    name: <PERSON><PERSON> and <PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy

      - uses: Swatinem/rust-cache@v2

      - name: Cargo fmt
        run: cargo fmt --all -- --check

      - name: "clippy --all"
        run: cargo clippy --all --all-features --tests -- -D warnings

      - run: cargo install cargo-all-features

      - name: Check all combinations of features can build
        run: cargo check-all-features

      - name: "cargo test"
        run: |
          cargo test --all
          cargo test --all --all-features
