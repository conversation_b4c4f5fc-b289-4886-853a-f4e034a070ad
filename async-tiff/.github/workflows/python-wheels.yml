# This file is (mostly) autogenerated by maturin v1.7.1
# To update, run
#
#    maturin generate-ci github -m python/Cargo.toml
#
name: Build wheels

on:
  push:
    tags:
      - "py-v*"
  workflow_dispatch:

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  linux:
    runs-on: ${{ matrix.platform.runner }}
    strategy:
      matrix:
        platform:
          - runner: ubuntu-latest
            target: x86_64
            manylinux: auto
          - runner: ubuntu-latest
            target: x86
            manylinux: auto
          - runner: ubuntu-latest
            target: aarch64
            manylinux: "2_24"
          - runner: ubuntu-latest
            target: armv7
            manylinux: auto
          - runner: ubuntu-latest
            target: s390x
            manylinux: auto
          - runner: ubuntu-latest
            target: ppc64le
            manylinux: auto
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          version: "0.5.x"

      - name: Install Python versions
        run: uv python install 3.9 3.10 3.11 3.12 3.13 pypy3.10

      - name: Build wheels
        uses: PyO3/maturin-action@v1
        with:
          target: ${{ matrix.platform.target }}
          args: --release --out dist -i 3.9 -i 3.10 -i 3.11 -i 3.12 -i 3.13 -i pypy3.10 --manifest-path python/Cargo.toml
          sccache: "true"
          manylinux: ${{ matrix.platform.manylinux }}
      - name: Upload wheels
        uses: actions/upload-artifact@v4
        with:
          name: wheels-linux-${{ matrix.platform.target }}
          path: dist

  musllinux:
    runs-on: ${{ matrix.platform.runner }}
    strategy:
      matrix:
        platform:
          - runner: ubuntu-latest
            target: x86_64
          - runner: ubuntu-latest
            target: x86
          - runner: ubuntu-latest
            target: aarch64
          - runner: ubuntu-latest
            target: armv7
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          version: "0.5.x"

      - name: Install Python versions
        run: uv python install 3.9 3.10 3.11 3.12 3.13 pypy3.10

      - name: Build wheels
        uses: PyO3/maturin-action@v1
        with:
          target: ${{ matrix.platform.target }}
          args: --release --out dist -i 3.9 -i 3.10 -i 3.11 -i 3.12 -i 3.13 -i pypy3.10 --manifest-path python/Cargo.toml
          sccache: "true"
          manylinux: musllinux_1_2
      - name: Upload wheels
        uses: actions/upload-artifact@v4
        with:
          name: wheels-musllinux-${{ matrix.platform.target }}
          path: dist

  # We skip windows for now because the symlinks (for `pyi` typing) give "access denied" on windows.

  # windows:
  #   runs-on: ${{ matrix.platform.runner }}
  #   strategy:
  #     matrix:
  #       platform:
  #         - runner: windows-latest
  #           target: x64
  #   steps:
  #     - uses: actions/checkout@v4
  #       with:
  #         submodules: "recursive"
  #     # There seem to be linking errors on Windows with the uv-provided Python
  #     # executables, so we use the Python versions provided by github actions
  #     # for now.
  #     # Seems to be this question: https://stackoverflow.com/questions/78557803/python-with-rust-cannot-open-input-file-python3-lib
  #     - uses: actions/setup-python@v5
  #       with:
  #         python-version: 3.13
  #         architecture: ${{ matrix.platform.target }}
  #     - name: Build wheels
  #       uses: PyO3/maturin-action@v1
  #       with:
  #         target: ${{ matrix.platform.target }}
  #         args: --release --out dist -i 3.9 -i 3.10 -i 3.11 -i 3.12 -i 3.13 --manifest-path python/Cargo.toml
  #         sccache: "true"
  #     - name: Upload wheels
  #       uses: actions/upload-artifact@v4
  #       with:
  #         name: wheels-windows-${{ matrix.platform.target }}
  #         path: dist

  macos:
    runs-on: ${{ matrix.platform.runner }}
    strategy:
      matrix:
        platform:
          - runner: macos-13
            target: x86_64
          - runner: macos-14
            target: aarch64
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          version: "0.5.x"

      - name: Install Python versions
        run: uv python install 3.9 3.10 3.11 3.12 3.13 pypy3.10

      - name: Build wheels
        uses: PyO3/maturin-action@v1
        with:
          target: ${{ matrix.platform.target }}
          args: --release --out dist -i 3.9 -i 3.10 -i 3.11 -i 3.12 -i 3.13 -i pypy3.10 --manifest-path python/Cargo.toml
          sccache: "true"
      - name: Upload wheels
        uses: actions/upload-artifact@v4
        with:
          name: wheels-macos-${{ matrix.platform.target }}
          path: dist

  # sdist:
  #   runs-on: ubuntu-latest
  #   strategy:
  #     matrix:
  #   steps:
  #     - uses: actions/checkout@v4
  #     - name: Build sdist
  #       uses: PyO3/maturin-action@v1
  #       with:
  #         command: sdist
  #         args: --out dist --manifest-path python/Cargo.toml
  #     - name: Upload sdist
  #       uses: actions/upload-artifact@v4
  #       with:
  #         name: wheels-sdist
  #         path: dist

  release:
    runs-on: ubuntu-latest
    name: Release
    # environment:
    #   name: release
    #   url: https://pypi.org/p/async-tiff
    # permissions:
    #   # IMPORTANT: this permission is mandatory for trusted publishing
    #   id-token: write
    if: startsWith(github.ref, 'refs/tags/')
    needs: [linux, musllinux, macos]
    steps:
      - uses: actions/download-artifact@v4
        with:
          pattern: wheels-*
          merge-multiple: true
          path: dist
      - uses: actions/setup-python@v5
        with:
          python-version: 3.9

      - uses: pypa/gh-action-pypi-publish@release/v1
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
