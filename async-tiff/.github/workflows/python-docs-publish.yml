name: Publish Python docs

# Only run on new tags starting with `py-v`
on:
  push:
    tags:
      - "py-v*"
  workflow_dispatch:

# https://stackoverflow.com/a/77412363
permissions:
  contents: write
  pages: write

jobs:
  build:
    name: Deploy Python docs
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: python
    steps:
      - uses: actions/checkout@v4
        # We need to additionally fetch the gh-pages branch for mike deploy
        with:
          fetch-depth: 0
          submodules: "true"

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - uses: Swatinem/rust-cache@v2

      - name: Install a specific version of uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          version: "0.5.x"

      - name: Set up Python 3.11
        run: uv python install 3.11

      - name: Install dependencies
        run: uv sync --no-install-package async-tiff

      - name: Build python packages
        run: |
          uv run --no-project maturin develop

      - name: Deploy docs
        env:
          GIT_COMMITTER_NAME: CI
          GIT_COMMITTER_EMAIL: <EMAIL>
        run: |
          # Get most recent git tag
          # https://stackoverflow.com/a/7261049
          # https://stackoverflow.com/a/3867811
          # We don't use {{github.ref_name}} because if triggered manually, it
          # will be a branch name instead of a tag version.
          # Then remove `py-` from the tag
          VERSION=$(git describe --tags --match="py-*" --abbrev=0 | cut -c 4-)

          # Only push publish docs as latest version if no letters in git tag
          # after the first character
          # (usually the git tag will have v as the first character)
          # Note the `cut` index is 1-ordered
          if echo $VERSION | cut -c 2- | grep -q "[A-Za-z]"; then
            echo "Is beta version"
            # For beta versions publish but don't set as latest
            uv run --no-project mike deploy $VERSION --update-aliases --push
          else
            echo "Is NOT beta version"
            uv run --no-project mike deploy $VERSION latest --update-aliases --push
          fi
