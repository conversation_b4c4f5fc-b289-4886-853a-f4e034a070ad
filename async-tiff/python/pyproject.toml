[build-system]
requires = ["maturin>=1.4.0,<2.0"]
build-backend = "maturin"

[project]
name = "async-tiff"
requires-python = ">=3.9"
dependencies = ["obspec>=0.1.0-beta.3"]
dynamic = ["version"]
classifiers = [
    "Programming Language :: Rust",
    "Programming Language :: Python :: Implementation :: CPython",
    "Programming Language :: Python :: Implementation :: PyPy",
]

[tool.maturin]
features = ["pyo3/extension-module"]
module-name = "async_tiff._async_tiff"
python-source = "python"

[tool.uv]
dev-dependencies = [
    # To enable following symlinks for pyi files
    "griffe>=1.6.0",
    "griffe-inherited-docstrings>=1.0.1",
    "ipykernel>=6.29.5",
    "maturin>=1.7.4",
    "mike>=2.1.3",
    "mkdocs-material[imaging]>=9.6.3",
    "mkdocs>=1.6.1",
    "mkdocstrings-python>=1.13.0",
    "mkdocstrings>=0.27.0",
    "numpy>=1",
    "obstore>=0.5.1",
    "pip>=24.2",
    "pytest-asyncio>=0.26.0",
    "pytest>=8.3.3",
    "ruff>=0.8.4",
]

[tool.pytest.ini_options]
addopts = "--color=yes"
asyncio_default_fixture_loop_scope="function"
asyncio_mode = "auto"
