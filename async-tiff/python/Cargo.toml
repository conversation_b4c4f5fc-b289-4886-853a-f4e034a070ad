[package]
name = "py-async-tiff"
version = "0.1.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
# description = "Fast, memory-efficient 2D spatial indexes for Python."
readme = "README.md"
repository = "https://github.com/developmentseed/async-tiff"
license = "MIT OR Apache-2.0"
keywords = ["python", "geospatial"]
categories = ["science::geo"]
rust-version = "1.75"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[lib]
name = "_async_tiff"
crate-type = ["cdylib"]

[dependencies]
async-tiff = { path = "../" }
bytes = "1.10.1"
futures = "0.3.31"
object_store = "0.12"
pyo3 = { version = "0.24.0", features = ["macros"] }
pyo3-async-runtimes = "0.24"
pyo3-bytes = "0.2"
pyo3-object_store = "0.2.0"
rayon = "1.10.0"
tokio-rayon = "2.1.0"
thiserror = "1"

# We opt-in to using rustls as the TLS provider for reqwest, which is the HTTP
# library used by object_store.
# https://github.com/seanmonstar/reqwest/issues/2025
reqwest = { version = "*", default-features = false, features = [
    "rustls-tls-native-roots",
] }

[profile.release]
lto = true
codegen-units = 1
