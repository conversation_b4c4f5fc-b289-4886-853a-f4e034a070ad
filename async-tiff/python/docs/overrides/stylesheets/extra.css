:root,
[data-md-color-scheme="default"] {
  /* --md-heading-font: "<PERSON>"; */
  --md-primary-fg-color: #cf3f02;
  --md-default-fg-color: #443f3f;
  --boxShadowD: 0px 12px 24px 0px rgba(68, 63, 63, 0.08),
    0px 0px 4px 0px rgba(68, 63, 63, 0.08);
}
body {
  margin: 0;
  padding: 0;
  /* font-size: 16px; */
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--md-heading-font);
  font-weight: bold;
}
.md-typeset h1,
.md-typeset h2 {
  font-weight: normal;
  color: var(--md-default-fg-color);
}
.md-typeset h3,
.md-typeset h4 {
  font-weight: bold;
  color: var(--md-default-fg-color);
}
.md-button,
.md-typeset .md-button {
  font-family: var(--md-heading-font);
}
.md-content .supheading {
  font-family: var(--md-heading-font);
  text-transform: uppercase;
  color: var(--md-primary-fg-color);
  font-size: 0.75rem;
  font-weight: bold;
}
