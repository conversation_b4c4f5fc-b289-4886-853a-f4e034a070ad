class GeoKeyDirectory:
    @property
    def model_type(self) -> int | None: ...
    @property
    def raster_type(self) -> int | None: ...
    @property
    def citation(self) -> str | None: ...
    @property
    def geographic_type(self) -> int | None: ...
    @property
    def geog_citation(self) -> str | None: ...
    @property
    def geog_geodetic_datum(self) -> int | None: ...
    @property
    def geog_prime_meridian(self) -> int | None: ...
    @property
    def geog_linear_units(self) -> int | None: ...
    @property
    def geog_linear_unit_size(self) -> float | None: ...
    @property
    def geog_angular_units(self) -> int | None: ...
    @property
    def geog_angular_unit_size(self) -> float | None: ...
    @property
    def geog_ellipsoid(self) -> int | None: ...
    @property
    def geog_semi_major_axis(self) -> float | None: ...
    @property
    def geog_semi_minor_axis(self) -> float | None: ...
    @property
    def geog_inv_flattening(self) -> float | None: ...
    @property
    def geog_azimuth_units(self) -> int | None: ...
    @property
    def geog_prime_meridian_long(self) -> float | None: ...
    @property
    def projected_type(self) -> int | None: ...
    @property
    def proj_citation(self) -> str | None: ...
    @property
    def projection(self) -> int | None: ...
    @property
    def proj_coord_trans(self) -> int | None: ...
    @property
    def proj_linear_units(self) -> int | None: ...
    @property
    def proj_linear_unit_size(self) -> float | None: ...
    @property
    def proj_std_parallel1(self) -> float | None: ...
    @property
    def proj_std_parallel2(self) -> float | None: ...
    @property
    def proj_nat_origin_long(self) -> float | None: ...
    @property
    def proj_nat_origin_lat(self) -> float | None: ...
    @property
    def proj_false_easting(self) -> float | None: ...
    @property
    def proj_false_northing(self) -> float | None: ...
    @property
    def proj_false_origin_long(self) -> float | None: ...
    @property
    def proj_false_origin_lat(self) -> float | None: ...
    @property
    def proj_false_origin_easting(self) -> float | None: ...
    @property
    def proj_false_origin_northing(self) -> float | None: ...
    @property
    def proj_center_long(self) -> float | None: ...
    @property
    def proj_center_lat(self) -> float | None: ...
    @property
    def proj_center_easting(self) -> float | None: ...
    @property
    def proj_center_northing(self) -> float | None: ...
    @property
    def proj_scale_at_nat_origin(self) -> float | None: ...
    @property
    def proj_scale_at_center(self) -> float | None: ...
    @property
    def proj_azimuth_angle(self) -> float | None: ...
    @property
    def proj_straight_vert_pole_long(self) -> float | None: ...
    @property
    def vertical(self) -> int | None: ...
    @property
    def vertical_citation(self) -> str | None: ...
    @property
    def vertical_datum(self) -> int | None: ...
    @property
    def vertical_units(self) -> int | None: ...
