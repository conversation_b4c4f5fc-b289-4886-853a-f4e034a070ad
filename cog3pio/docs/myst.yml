# See docs at: https://mystmd.org/guide/frontmatter
version: 1
project:
  id: 0c0b30a3-67fd-4260-bea5-1d77fb40395d
  title: "cog3pio"
  description: "Cloud-optimized GeoTIFF ... Parallel I/O"
  # keywords: []
  # authors: []
  github: https://github.com/weiji14/cog3pio
  # To autogenerate a Table of Contents, run "jupyter book init --write-toc"
  toc:
    # Auto-generated by `myst init --write-toc`
    - file: index.md
    - file: quickstart.md
    - pattern: _build/myst-asts/api.myst.json
    - file: changelog.md

site:
  template: book-theme
  options:
    hide_toc: true
    hide_footer_links: true
    # favicon: favicon.ico
    # logo: site_logo.png
