# ⚙️ *cog3pio* - Cloud-optimized GeoTIFF ... Parallel I/O

*Practice the Three Acts of CoGnizance - Use GeoTIFF-tiles, Be GDAL-less, Go GPU-accelerated*

## Installation

### Rust

```bash
cargo add cog3pio
```

### Python

```bash
pip install cog3pio
```

> [!TIP]
> The API for this crate/library is still unstable and subject to change, so you may
> want to pin to a specific git commit using either:
> - `cargo add --git https://github.com/weiji14/cog3pio.git --rev <sha>`
> - `pip install git+https://github.com/weiji14/cog3pio.git@<sha>`
>
> where `<sha>` is a commit hashsum obtained from
> https://github.com/weiji14/cog3pio/commits/main

## User Guide

[Quickstart](quickstart.html) | [API Reference](api.html) | [Changelog](changelog.html)
