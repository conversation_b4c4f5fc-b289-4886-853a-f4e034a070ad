# Run linters
#
# Lint Rust code using cargo clippy, and GitHub Actions workflows using zizmor. Apply
# static analysis rules to catch common mistakes and improves code style.

name: Lint

on:
  push:
    branches: ["main"]
  pull_request:
    types: [opened, reopened, synchronize]
    branches: ["main"]

permissions: {}

# Make sure CI fails on all warnings, including Clippy lints
env:
  CARGO_TERM_COLOR: always
  RUSTFLAGS: "-Dwarnings"

jobs:
  clippy_check:
    runs-on: ubuntu-24.04

    steps:
      - name: Checkout repository
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          persist-credentials: false

      - name: Run Clippy
        run: cargo clippy --all-targets

  format_check:
    runs-on: ubuntu-24.04

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Run Rustfmt
        run: cargo fmt --check -- --config imports_granularity=Module,group_imports=StdExternalCrate

  zizmor:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          persist-credentials: false

      - name: Install the latest version of uv
        uses: astral-sh/setup-uv@f94ec6bedd8674c4426838e6b50417d36b6ab231 # v5.3.1

      - name: Run zizmor 🌈
        run: uvx zizmor --color=always .
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
