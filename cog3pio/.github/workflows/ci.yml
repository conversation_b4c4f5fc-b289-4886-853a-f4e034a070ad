# This file is autogenerated by maturin v1.8.7
# To update, run
#
#    maturin generate-ci --pytest github
#
name: CI

on:
  push:
    branches: ["main"]
  release:
    types: [published]
  pull_request:
    types: [opened, reopened, synchronize]
    branches: ["main"]

env:
  CARGO_TERM_COLOR: always

permissions:
  contents: read

jobs:
  rust:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        toolchain:
          - 1.85.0 # msrv
          - stable
          - beta
          - nightly
    steps:
      - name: Checkout repository
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
        with:
          persist-credentials: false

      - name: Update Rust toolchain
        run: rustup update ${{ matrix.toolchain }} && rustup default ${{ matrix.toolchain }}

      - name: Build
        run: cargo build --verbose

      - name: Run tests
        run: cargo test --verbose

  linux:
    runs-on: ${{ matrix.platform.runner || 'ubuntu-24.04' }}
    strategy:
      matrix:
        platform:
          - target: x86_64
          - target: aarch64
            runner: ubuntu-24.04-arm
          - target: armv7
          - target: s390x
          - target: ppc64le
    steps:
      - name: Checkout repository
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          persist-credentials: false

      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: "3.12"

      - name: Build wheels
        uses: PyO3/maturin-action@aef21716ff3dcae8a1c301d23ec3e4446972a6e3 # v1.49.1
        with:
          target: ${{ matrix.platform.target }}
          args: --release --out dist
          sccache: ${{ !startsWith(github.ref, 'refs/tags/') }}
          manylinux: "2_28"

      - name: Build free-threaded wheels
        uses: PyO3/maturin-action@aef21716ff3dcae8a1c301d23ec3e4446972a6e3 # v1.49.1
        with:
          target: ${{ matrix.platform.target }}
          args: --release --out dist --interpreter python3.13t
          sccache: ${{ !startsWith(github.ref, 'refs/tags/') }}
          manylinux: "2_28"

      - name: Upload wheels
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: wheels-linux-${{ matrix.platform.target }}
          path: dist/

      - name: pytest
        if: ${{ endswith(matrix.platform.target, '64') }} # x86_64 and aarch64
        shell: bash
        run: |
          set -e
          python3 -m venv .venv
          source .venv/bin/activate
          pip install cog3pio[tests] --find-links dist --force-reinstall
          pytest --verbose

      - name: pytest
        if: ${{ !endswith(matrix.platform.target, '64') && !startsWith(github.ref, 'refs/tags/') }} # armv7, s390x and ppc64le
        uses: uraimo/run-on-arch-action@d94c13912ea685de38fccc1109385b83fd79427d # v3.0.1
        with:
          arch: ${{ matrix.platform.target }}
          distro: ubuntu24.04
          githubToken: ${{ github.token }}
          install: |
            apt update
            apt install -y --no-install-recommends \
                        gcc g++ gfortran libopenblas-dev liblapack-dev ninja-build \
                        pkg-config python3-pip python3-dev python3-venv
          run: |
            set -e
            python3 -m venv .venv
            source .venv/bin/activate
            pip3 install -U pip
            pip3 install cog3pio[tests] --find-links dist --force-reinstall
            pytest --verbose

  windows:
    runs-on: windows-2025
    strategy:
      matrix:
        target: [x64]
    steps:
      - name: Checkout repository
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          persist-credentials: false

      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: "3.12"
          architecture: ${{ matrix.target }}

      - name: Build wheels
        uses: PyO3/maturin-action@aef21716ff3dcae8a1c301d23ec3e4446972a6e3 # v1.49.1
        with:
          target: ${{ matrix.target }}
          args: --release --out dist
          sccache: ${{ !startsWith(github.ref, 'refs/tags/') }}

      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: "3.13t"
          architecture: ${{ matrix.target }}

      - name: Build free-threaded wheels
        uses: PyO3/maturin-action@aef21716ff3dcae8a1c301d23ec3e4446972a6e3 # v1.49.1
        with:
          target: ${{ matrix.target }}
          args: --release --out dist -i python3.13t
          sccache: ${{ !startsWith(github.ref, 'refs/tags/') }}

      - name: Upload wheels
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: wheels-windows-${{ matrix.target }}
          path: dist/

      - name: pytest
        shell: bash
        run: |
          set -e
          python3 -m venv .venv
          source .venv/Scripts/activate
          pip install cog3pio[tests] --find-links dist --force-reinstall
          pytest --verbose

  macos:
    runs-on: ${{ matrix.platform.runner }}
    strategy:
      matrix:
        platform:
          - runner: macos-13
            target: x86_64
          - runner: macos-15
            target: aarch64
    steps:
      - name: Checkout repository
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          persist-credentials: false

      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: "3.12"

      - name: Build wheels
        uses: PyO3/maturin-action@aef21716ff3dcae8a1c301d23ec3e4446972a6e3 # v1.49.1
        with:
          target: ${{ matrix.platform.target }}
          args: --release --out dist
          sccache: ${{ !startsWith(github.ref, 'refs/tags/') }}

      - name: Build free-threaded wheels
        uses: PyO3/maturin-action@aef21716ff3dcae8a1c301d23ec3e4446972a6e3 # v1.49.1
        with:
          target: ${{ matrix.platform.target }}
          args: --release --out dist -i python3.13t
          sccache: ${{ !startsWith(github.ref, 'refs/tags/') }}

      - name: Upload wheels
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: wheels-macos-${{ matrix.platform.target }}
          path: dist/

      - name: pytest
        run: |
          set -e
          python3 -m venv .venv
          source .venv/bin/activate
          pip install cog3pio[tests] --find-links dist --force-reinstall
          pytest --verbose

  sdist:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          persist-credentials: false

      - name: Build sdist
        uses: PyO3/maturin-action@aef21716ff3dcae8a1c301d23ec3e4446972a6e3 # v1.49.1
        with:
          command: sdist
          args: --out dist

      - name: Upload sdist
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: wheels-sdist
          path: dist/

  publish-to-testpypi:
    name: Publish Python 🐍 distribution 📦 to TestPyPI
    if: startsWith(github.ref, 'refs/tags/')
    needs: [linux, windows, macos, sdist]
    runs-on: ubuntu-24.04
    environment:
      name: testpypi
      url: https://test.pypi.org/project/cog3pio
    permissions:
      id-token: write # IMPORTANT: mandatory for trusted OIDC publishing

    steps:
      - name: Download built wheels
        uses: actions/download-artifact@95815c38cf2ff2164869cbab79da8d1f422bc89e # v4.2.1
        with:
          path: dist/
          merge-multiple: true

      - name: Publish distribution 📦 to TestPyPI
        uses: pypa/gh-action-pypi-publish@76f52bc884231f62b9a034ebfe128415bbaabdfc # v1.12.4
        with:
          repository-url: https://test.pypi.org/legacy/
          verbose: true

  publish-to-pypi:
    name: Publish Python 🐍 distribution 📦 to PyPI
    runs-on: ubuntu-24.04
    environment:
      name: pypi
      url: https://pypi.org/project/cog3pio/
    if: github.event.release.prerelease == false && startsWith(github.ref, 'refs/tags/')
    needs: [linux, windows, macos, sdist]
    permissions:
      id-token: write # IMPORTANT: mandatory for trusted OIDC publishing
    steps:
      - name: Download built wheels
        uses: actions/download-artifact@95815c38cf2ff2164869cbab79da8d1f422bc89e # v4.2.1
        with:
          path: dist/
          merge-multiple: true

      - name: Publish distribution 📦 to PyPI
        uses: pypa/gh-action-pypi-publish@76f52bc884231f62b9a034ebfe128415bbaabdfc # v1.12.4
        with:
          verbose: true
